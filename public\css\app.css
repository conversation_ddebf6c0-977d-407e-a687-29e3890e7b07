/*
TTAJet Courier Service - Unified & Restructured Stylesheet

TABLE OF CONTENTS
------------------
1.  GLOBAL & BASE STYLES
    - Root & Body
    - Box Sizing & Viewport
    - Custom Scrollbar
2.  BRAND & COLOR PALETTE
3.  LAYOUT & CONTAINERS
4.  TYPOGRAPHY
5.  NAVIGATION
    - Header
    - Mobile Menu
    - Mobile Bottom Navigation
6.  COMPONENTS
    - Buttons
    - Cards (Base, Service, Price, Glassmorphism)
    - Forms & Inputs
    - Notifications & Spinners
    - Social Icons
7.  PAGE-SPECIFIC SECTIONS
    - Hero Section
    - Pricing Section
    - Review Section
    - Newsletter Section
    - Footer
8.  ANIMATIONS & TRANSITIONS
    - Keyframe Animations
    - Animation Utility Classes
    - JavaScript Animation Hooks
9.  UTILITY & HELPER CLASSES
10. DASHBOARD STYLES
    - Layout & Base
    - Cards & Stats
    - Tables
    - Tabs & Navigation
    - Buttons & Actions
    - Filters & Search
    - Charts
    - Badges
    - Pagination
    - Empty States
    - Accessibility & States
11. RESPONSIVE DESIGN (MOBILE-FIRST)
    - Base Mobile Overrides (Up to 768px)
    - Touch-Friendly Improvements
    - Tablet Portrait (481px - 768px)
    - Tablet Landscape & Small Desktop (769px - 1024px)
    - Small Mobile (Up to 480px)
    - Extra Small Mobile (Up to 375px)
    - Very Small Mobile (Up to 320px)
    - High DPI Screens
12. FINAL OVERRIDES & VIEWPORT CONSTRAINTS
*/

/* ===== 1. GLOBAL & BASE STYLES ===== */

/* Root & Body */
body {
    font-family: 'Poppins', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #F9FAFB; /* Light gray base background */
}

/* Box Sizing & Viewport */
* {
    box-sizing: border-box;
    max-width: 100%; /* Fix potential layout issues */
}

html, body {
    overflow-x: hidden;
    max-width: 100vw;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #F97316;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #ea580c;
}

/* ===== 2. BRAND & COLOR PALETTE ===== */
.brand-orange {
    background-color: #F97316;
}

.brand-orange-text {
    color: #F97316;
}

.brand-orange-border {
    border-color: #F97316;
}

/* ===== 3. LAYOUT & CONTAINERS ===== */
.container {
    padding-left: 2rem;
    padding-right: 2rem;
}

section {
    padding-top: 3rem;
    padding-bottom: 3rem;
}

/* ===== 4. TYPOGRAPHY ===== */
.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ===== 5. NAVIGATION ===== */

/* Header */
header {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

header img {
    height: 50px !important;
}

/* Mobile Menu Button */
.mobile-menu-button {
    display: block;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
    z-index: 60;
    position: relative;
}

.mobile-menu-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.mobile-menu-button:focus {
    outline: 2px solid #F97316;
    outline-offset: 2px;
}

/* Mobile Navigation Menu */
.mobile-nav-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 55;
}

.mobile-menu {
    display: none !important;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.2s ease-in-out;
    margin-top: 0;
    border-radius: 0;
}

.mobile-menu.show {
    display: block !important;
    opacity: 1;
    transform: translateY(0);
}

/* Alpine.js specific helpers for mobile menu */
[x-cloak] {
    display: none !important;
}

[x-show="mobileMenuOpen"] {
    display: none !important;
}

[x-show="mobileMenuOpen"][style*="display: block"] {
    display: block !important;
}

/* Mobile Bottom Navigation - Legacy (keeping for compatibility) */
.mobile-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    padding: 0.5rem 0;
    display: none; /* Hidden by default */
}

.mobile-bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    color: #9CA3AF;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.75rem;
    min-height: 60px;
}

.mobile-bottom-nav-item:hover,
.mobile-bottom-nav-item.active {
    color: #F97316;
    transform: translateY(-2px);
}

.mobile-bottom-nav-item i {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
}

.mobile-bottom-nav-item span {
    font-size: 0.625rem;
    font-weight: 500;
}

/* Modern Mobile Bottom Navigation - Glassmorphism Style */
.mobile-bottom-nav-modern {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    display: none; /* Hidden by default */
    padding: 0 10px;
}

.mobile-nav-pill {
    /* Apple-style glassmorphism effect */
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(40px) saturate(180%);
    -webkit-backdrop-filter: blur(40px) saturate(180%);
    border-radius: 50px;
    padding: 8px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 2px 8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.18);
    display: flex;
    align-items: center;
    justify-content: space-around;
    min-width: 95vw;
    max-width: 98vw;
    position: relative;
}

/* Additional glassmorphism layer */
.mobile-nav-pill::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: 50px;
    pointer-events: none;
}

.mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 14px;
    color: rgba(17, 17, 17, 0.8);
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.75rem;
    border-radius: 40px;
    position: relative;
    flex: 1;
    min-height: 56px;
    z-index: 1;
}

/* Hover state with brand orange */
.mobile-nav-item:hover,
.mobile-nav-item.hover {
    background: rgba(249, 115, 22, 0.15);
    color: #F97316;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.2);
}

/* Active state with brand orange */
.mobile-nav-item.active {
    background: linear-gradient(135deg, #F97316, #EA580C) !important;
    color: #FFFFFF !important;
    transform: translateY(-2px);
    box-shadow:
        0 6px 20px rgba(249, 115, 22, 0.4),
        0 2px 8px rgba(249, 115, 22, 0.3);
}

/* Pressed state for touch feedback */
.mobile-nav-item.pressed {
    transform: translateY(0) scale(0.95);
    transition-duration: 0.1s;
}

/* Click animation */
.mobile-nav-item.clicked {
    transform: translateY(0) scale(0.9);
    transition-duration: 0.1s;
}

.mobile-nav-item i {
    font-size: 1.1rem;
    margin-bottom: 4px;
    transition: all 0.3s ease;
}

.mobile-nav-item span {
    font-size: 0.65rem;
    font-weight: 500;
    letter-spacing: 0.025em;
    transition: all 0.3s ease;
}

.mobile-nav-item.active i,
.mobile-nav-item:hover i,
.mobile-nav-item.hover i {
    font-size: 1.15rem;
}

.mobile-nav-item.active span,
.mobile-nav-item:hover span,
.mobile-nav-item.hover span {
    font-weight: 600;
}

/* Touch and accessibility improvements */
.mobile-nav-item:focus {
    outline: 2px solid #F97316;
    outline-offset: 2px;
    background: rgba(249, 115, 22, 0.1);
}

.mobile-nav-item:focus-visible {
    outline: 2px solid #F97316;
    outline-offset: 2px;
}

/* Enhanced glassmorphism animations */
@media (prefers-reduced-motion: no-preference) {
    .mobile-nav-pill {
        animation: slideUpGlass 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .mobile-nav-item {
        animation: fadeInScale 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        animation-fill-mode: both;
    }

    .mobile-nav-item:nth-child(1) { animation-delay: 0.1s; }
    .mobile-nav-item:nth-child(2) { animation-delay: 0.15s; }
    .mobile-nav-item:nth-child(3) { animation-delay: 0.2s; }
    .mobile-nav-item:nth-child(4) { animation-delay: 0.25s; }
}

@keyframes slideUpGlass {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(30px);
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
        backdrop-filter: blur(40px);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}


/* ===== 6. COMPONENTS ===== */

/* Buttons */
.btn-primary {
    background-color: #F97316;
    color: white;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #ea580c;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Cards */
.card-shadow {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.card-shadow-lg {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.service-card, .price-card {
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.price-card:hover {
    transform: translateY(-4px);
}

/* Enhanced Glassmorphism Effect */
.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Forms & Inputs */
.form-input {
    padding-left: 3.5rem !important;
}

.form-input-icon {
    position: absolute;
    left: 1.25rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9CA3AF;
    pointer-events: none;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    box-shadow: 0 0 0 2px #F97316;
    border-color: #F97316;
}

/* Notifications & Spinners */
.toast-notification {
    min-width: 300px;
    max-width: 500px;
}

.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #F97316;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

/* Social Icons */
.social-icon {
    width: 36px;
    height: 36px;
    background-color: #202020;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.social-icon:hover {
    background-color: #F97316;
    color: white;
    transform: translateY(-2px);
}

/* ===== 7. PAGE-SPECIFIC SECTIONS ===== */

/* Hero Section */
.hero-section {
    background-image: url('https://img.freepik.com/free-vector/worldwide-global-map-outline-black-background_1017-46153.jpg');
    background-size: cover;
    background-position: center;
}

/* Pricing Section */
#pricing {
    background-image: linear-gradient(to top right, #181818, #000000);
}

/* Review Section */
#review-card {
    transition: all 0.3s ease;
}

/* Newsletter Section */
.newsletter-section {
    background: linear-gradient(190deg, #2222225d 0%, #00000098 100%);
}

/* Footer */
footer {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
}

/* ===== 8. ANIMATIONS & TRANSITIONS ===== */

/* Keyframe Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Animation Utility Classes */
.fade-in { animation: fadeIn 0.6s ease-in-out; }
.slide-up { animation: slideUp 0.6s ease-out; } /* This seems to be a duplicate keyframe name, but keeping as is */
.mobile-menu-slide-down { animation: slideDown 0.2s ease-out; }
.mobile-menu-slide-up { animation: slideUp 0.2s ease-in; } /* Renaming this keyframe would be ideal to avoid conflict */

/* JavaScript Animation Hooks (GSAP, etc.) */
/* Fallback styles to ensure elements are visible if JS fails */
.text-content > *,
.booking-form,
.service-card,
.price-card,
#about-preview *,
#reviews * {
    opacity: 1 !important;
    transform: translateY(0) scale(1) !important;
    visibility: visible !important;
}

/* Hide elements if JavaScript is enabled and animations are active */
body.js-animations-enabled .text-content > *,
body.js-animations-enabled .booking-form,
body.js-animations-enabled .service-card,
body.js-animations-enabled .price-card {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
}

/* Ensure non-homepage pages are not affected by homepage animations */
body:not(.homepage) .text-content > *,
body:not(.homepage) .booking-form,
body:not(.homepage) .service-card,
body:not(.homepage) .price-card {
    opacity: 1 !important;
    transform: translateY(0) scale(1) !important;
    visibility: visible !important;
}


/* ===== 9. UTILITY & HELPER CLASSES ===== */
.backdrop-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}


/* ===== 10. DASHBOARD STYLES ===== */

/* Dashboard Layout & Base */
.dashboard-container {
    overflow-x: hidden;
    max-width: 100vw;
}
.dashboard-content {
    max-width: 100%;
    overflow-x: hidden;
}

/* Dashboard Cards & Stats */
.dashboard-card, .dashboard-stat-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    border: 1px solid #f3f4f6;
}
.dashboard-stat-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}
.dashboard-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Dashboard Tables */
.dashboard-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}
.dashboard-table {
    min-width: 100%;
    border-collapse: collapse;
}

/* Dashboard Tabs & Navigation */
.dashboard-tabs {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
}
.dashboard-tabs::-webkit-scrollbar {
    display: none;
}
.dashboard-tab {
    flex-shrink: 0;
    padding: 1rem 1.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-height: 44px;
    display: flex;
    align-items: center;
}
.dashboard-tab.active {
    border-bottom-color: #f97316;
    color: #f97316;
}
.dashboard-tab:hover {
    color: #374151;
    border-bottom-color: #d1d5db;
}

/* Dashboard Buttons & Actions */
.dashboard-icon-btn {
    min-width: 44px;
    min-height: 44px;
    padding: 0.75rem;
    border-radius: 0.5rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    border: 1px solid transparent;
    position: relative;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}
.dashboard-icon-btn i {
    font-size: 1rem;
}
.dashboard-icon-btn.has-text {
    padding: 0.75rem 1rem;
    gap: 0.5rem;
}
.dashboard-btn-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}
.dashboard-btn-group .dashboard-icon-btn {
    flex-shrink: 0;
}
.dashboard-header-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Dashboard Button States (Color, Loading, etc.) */
.dashboard-icon-btn.primary { background-color: #f97316; color: white; border-color: #f97316; }
.dashboard-icon-btn.primary:hover { background-color: #ea580c; border-color: #ea580c; }
.dashboard-icon-btn.secondary { background-color: white; color: #374151; border-color: #d1d5db; }
.dashboard-icon-btn.secondary:hover { background-color: #f9fafb; border-color: #9ca3af; }
.dashboard-icon-btn.danger { background-color: #dc2626; color: white; border-color: #dc2626; }
.dashboard-icon-btn.danger:hover { background-color: #b91c1c; border-color: #b91c1c; }
.dashboard-icon-btn.success { background-color: #10b981; border-color: #10b981; color: white; }
.dashboard-icon-btn.success:hover { background-color: #059669; border-color: #059669; }
.dashboard-icon-btn.error { background-color: #ef4444; border-color: #ef4444; color: white; }
.dashboard-icon-btn.error:hover { background-color: #dc2626; border-color: #dc2626; }
.dashboard-icon-btn.warning { background-color: #f59e0b; border-color: #f59e0b; color: white; }
.dashboard-icon-btn.warning:hover { background-color: #d97706; border-color: #d97706; }
.dashboard-icon-btn.info { background-color: #3b82f6; border-color: #3b82f6; color: white; }
.dashboard-icon-btn.info:hover { background-color: #2563eb; border-color: #2563eb; }
.dashboard-icon-btn:disabled, .dashboard-icon-btn.disabled { opacity: 0.5; cursor: not-allowed; pointer-events: none; }
.dashboard-icon-btn.loading { pointer-events: none; opacity: 0.7; }
.dashboard-icon-btn.loading i { animation: spin 1s linear infinite; }

/* Dashboard Filters & Search */
.dashboard-filters {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}
.dashboard-search-container {
    position: relative;
    width: 100%;
    max-width: 400px;
}
.dashboard-search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    background-color: white;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}
.dashboard-search-input:focus {
    outline: none;
    border-color: #f97316;
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
}
.dashboard-search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 0.875rem;
}

/* Dashboard Charts */
.dashboard-chart-container {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    border: 1px solid #f3f4f6;
}

/* Dashboard Badges */
.dashboard-status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

/* Dashboard Pagination */
.dashboard-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
}
.dashboard-pagination .page-link {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    color: #374151;
    text-decoration: none;
    transition: all 0.2s ease;
}
.dashboard-pagination .page-link:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
}
.dashboard-pagination .page-link.active {
    background-color: #f97316;
    border-color: #f97316;
    color: white;
}

/* Dashboard Empty States */
.dashboard-empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
    color: #6b7280;
}
.dashboard-empty-state i {
    font-size: 3rem;
    color: #d1d5db;
    margin-bottom: 1rem;
}
.dashboard-empty-state h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}
.dashboard-empty-state p {
    font-size: 0.875rem;
    margin-bottom: 1.5rem;
}

/* Dashboard Accessibility & States */
.dashboard-icon-btn:focus {
    outline: 2px solid #f97316;
    outline-offset: 2px;
}
.dashboard-icon-btn:active {
    transform: scale(0.98);
}
/* Tooltip for icon-only buttons */
.dashboard-icon-btn[title]:not(.has-text):hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #374151;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 0.25rem;
}
.dashboard-icon-btn[title]:not(.has-text):hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #374151;
    z-index: 1000;
}

/* ===== 11. RESPONSIVE DESIGN (MOBILE-FIRST) ===== */

/* Base Mobile Overrides (Up to 768px) */
@media (max-width: 768px) {
    /* Layout & Spacing */
    main { margin-top: 80px !important; }
    body { padding-bottom: 100px; } /* Increased for modern nav */
    section { margin-bottom: 2rem; }
    .grid { gap: 1rem; }
    .grid.gap-8 { gap: 1.5rem; }
    .grid.gap-12 { gap: 2rem; }
    .grid.gap-16 { gap: 2.5rem; }
    .py-20 { padding-top: 3rem; padding-bottom: 3rem; }
    .py-28 { padding-top: 4rem; padding-bottom: 4rem; }
    .mb-16 { margin-bottom: 2rem; }
    .mt-10 { margin-top: 2rem; }

    /* Typography */
    .text-3xl { font-size: 1.75rem; }
    .text-4xl { font-size: 2rem; }
    .text-5xl { font-size: 2.5rem; }
    .text-7xl { font-size: 3rem; }

    /* Navigation */
    .md\:hidden { display: block !important; }
    .mobile-menu-button {
        min-width: 44px;
        min-height: 44px;
        display: flex !important;
        align-items: center;
        justify-content: center;
    }
    .mobile-bottom-nav { display: none; } /* Hide legacy nav */
    .mobile-bottom-nav-modern { display: block; } /* Show modern nav */
    #scroll-to-top { bottom: 120px; right: 10px} /* Adjust for new nav position */
    .mobile-menu { max-height: calc(100vh - 80px); overflow-y: auto; }
    header [x-show="mobileMenuOpen"] {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        width: 100%;
        z-index: 999;
        display: none !important; /* Force hidden */
    }
    header [x-show="mobileMenuOpen"].show,
    header [x-show="mobileMenuOpen"][style*="display: block"].show {
        display: block !important; /* Force shown */
    }

    /* Forms */
    .booking-form { padding: 1rem; }
    .booking-form .grid { grid-template-columns: 1fr; gap: 1rem; }
    input[type="text"], input[type="email"], input[type="tel"], input[type="password"], select, textarea {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        border-radius: 8px;
        font-size: 16px !important; /* Prevent iOS zoom */
    }

    /* Cards */
    .rounded-2xl { border-radius: 1rem; }
    .shadow-xl { box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
    .service-card, .price-card { margin-left: auto; margin-right: auto; }

    /* Images */
    img { max-width: 100%; height: auto; }

    /* Tables */
    table { font-size: 14px; }
    
    /* ===== DASHBOARD MOBILE STYLES (up to 768px) ===== */
    .dashboard-grid, .dashboard-grid.two-col, .dashboard-grid.three-col { grid-template-columns: 1fr; gap: 1rem; }
    .dashboard-grid.four-col { grid-template-columns: repeat(2, 1fr); }
    .dashboard-container .container { padding-left: 1rem; padding-right: 1rem; }
    .dashboard-card, .dashboard-stat-card { padding: 1rem; border-radius: 0.5rem; }
    .dashboard-stat-card:hover { transform: none; }
    
    /* Dashboard Tables (Card Layout) */
    .dashboard-table-container { overflow-x: visible; }
    .dashboard-table, .dashboard-table thead, .dashboard-table tbody, .dashboard-table th, .dashboard-table td, .dashboard-table tr { display: block; }
    .dashboard-table thead tr { position: absolute; top: -9999px; left: -9999px; }
    .dashboard-table tr { background: white; border: 1px solid #e5e7eb; border-radius: 0.5rem; margin-bottom: 1rem; padding: 1rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); }
    .dashboard-table td { border: none; padding: 0.5rem 0; position: relative; padding-left: 40% !important; text-align: left !important; }
    .dashboard-table td:before { content: attr(data-label) ": "; position: absolute; left: 0; width: 35%; padding-right: 10px; white-space: nowrap; font-weight: 600; color: #374151; font-size: 0.875rem; }
    .dashboard-table td:first-child { border-top: none; font-weight: 600; font-size: 1rem; color: #111827; padding-left: 0 !important; margin-bottom: 0.5rem; }
    .dashboard-table td:first-child:before { display: none; }

    /* Dashboard Tabs (Vertical) */
    .dashboard-tabs { flex-direction: column; border-bottom: none; border-right: 1px solid #e5e7eb; overflow-x: visible; overflow-y: auto; max-height: 300px; }
    .dashboard-tab { border-bottom: none; border-right: 2px solid transparent; padding: 0.75rem 1rem; text-align: left; width: 100%; }
    .dashboard-tab.active { border-right-color: #f97316; border-bottom-color: transparent; background-color: #fef3f2; }

    /* Dashboard Buttons & Actions */
    .dashboard-icon-btn { min-width: 48px; min-height: 48px; padding: 0.875rem; }
    .dashboard-icon-btn i { font-size: 1.125rem; }
    .dashboard-icon-btn.has-text { padding: 0.875rem 1.25rem; gap: 0.75rem; }
    .dashboard-actions, .dashboard-header-actions { display: flex; flex-direction: column; gap: 0.75rem; width: 100%; }
    .dashboard-header-actions { margin-top: 1rem; }
    .dashboard-actions .dashboard-icon-btn, .dashboard-header-actions .dashboard-icon-btn { width: 100%; justify-content: center; }

    /* Dashboard Filters & Search */
    .dashboard-filters { padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem; }
    .dashboard-filters .grid, .dashboard-filters .flex { flex-direction: column; grid-template-columns: 1fr; gap: 1rem; }
    .dashboard-filters .flex > * { width: 100%; }
    .dashboard-search-container { max-width: 100%; margin-bottom: 1rem; }
    .dashboard-search-input { padding: 1rem 1rem 1rem 3rem; font-size: 16px; }
    .dashboard-search-icon { left: 1rem; font-size: 1rem; }

    /* Dashboard Charts, Badges, Pagination, Empty State */
    .dashboard-chart-container { padding: 1rem; border-radius: 0.5rem; }
    .dashboard-chart-container canvas { max-height: 250px !important; }
    .dashboard-status-badge { padding: 0.375rem 0.875rem; font-size: 0.8125rem; }
    .dashboard-pagination { gap: 0.25rem; margin-top: 1rem; }
    .dashboard-pagination .page-link { min-width: 40px; min-height: 40px; font-size: 0.875rem; }
    .dashboard-empty-state { padding: 2rem 1rem; }
    .dashboard-empty-state i { font-size: 2.5rem; }
    .dashboard-empty-state h3 { font-size: 1rem; }
    .dashboard-empty-state p { font-size: 0.8125rem; }

    /* Mobile-first responsive utilities */
    .mobile-p-2 { padding: 0.5rem; }
    .mobile-p-4 { padding: 1rem; }
    .mobile-p-6 { padding: 1.5rem; }
    .mobile-m-2 { margin: 0.5rem; }
    .mobile-m-4 { margin: 1rem; }
    .mobile-m-6 { margin: 1.5rem; }
    .mobile-mb-2 { margin-bottom: 0.5rem; }
    .mobile-mb-4 { margin-bottom: 1rem; }
    .mobile-mb-6 { margin-bottom: 1.5rem; }
    .mobile-text-xs { font-size: 0.75rem; }
    .mobile-text-sm { font-size: 0.875rem; }
    .mobile-text-base { font-size: 1rem; }
    .mobile-text-lg { font-size: 1.125rem; }
    .mobile-hidden { display: none; }
    .mobile-block { display: block; }
    .mobile-flex { display: flex; }
    .mobile-grid { display: grid; }
    .mobile-w-full { width: 100%; }
    .mobile-w-auto { width: auto; }
    .mobile-flex-col { flex-direction: column; }
    .mobile-flex-row { flex-direction: row; }
    .mobile-justify-center { justify-content: center; }
    .mobile-items-center { align-items: center; }
    .mobile-gap-2 { gap: 0.5rem; }
    .mobile-gap-4 { gap: 1rem; }
    .mobile-gap-6 { gap: 1.5rem; }
}

/* Landscape Mobile Orientation */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-section {
        min-height: 100vh;
        display: grid;
        grid-template-columns: 100%;
        padding-top: 2rem;
    }
    .hero-section:first-child {
        order: -1;
    }
    .hero-section h1 {
        font-size: 2rem !important;
    }
    .hero-section .grid {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        align-items: center;
    }
}

/* Touch-Friendly Improvements */
@media (hover: none) and (pointer: coarse) {
    button, a, input, select, textarea {
        min-height: 44px;
        min-width: 44px;
    }
    .service-card:hover, .price-card:hover {
        transform: none;
    }
    button:active, a:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
}
@media (max-width: 768px) and (pointer: coarse) {
    .dashboard-table-container, .dashboard-tabs {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }
    .dashboard-icon-btn:active, .dashboard-tab:active {
        background-color: rgba(0, 0, 0, 0.1);
    }
}

/* Tablet Portrait (481px to 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .hero-section h1 {
        font-size: 3.5rem;
    }
    .hero-section .grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
    .service-card, .price-card {
        margin-bottom: 2rem;
    }
    .grid.sm\:grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

/* Tablet Landscape and Small Desktop (769px to 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .hero-section h1 {
        font-size: 4rem;
    }
    .container {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* Small Mobile (up to 480px) */
@media (max-width: 480px) {
    .heross {
        display: flex;
        flex-direction: column-reverse;
    }

    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    .hero-section {
        min-height: 100vh;
        padding-top: 6rem;
    }
    .hero-section h1 {
        font-size: 2.5rem !important;
        line-height: 1.1;
        margin-bottom: 1rem;
    }
    .hero-section p {
        font-size: 1rem;
        line-height: 1.5;
        margin-bottom: 1.5rem;
    }
    .hero-section .grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    .hero-section .flex {
        flex-direction: column;
        gap: 1rem;
    }
    .hero-section a {
        padding: 1rem 2rem;
        font-size: 1rem;
        text-align: center;
        width: 100%;
    }
    .service-card, .price-card {
        margin-bottom: 1.5rem;
        padding: 1.5rem;
    }
    .service-card .w-20 {
        width: 4rem;
        height: 4rem;
        margin-bottom: 1rem;
    }
    .service-card i { font-size: 2rem; }
    .price-card i { font-size: 2.5rem; }
    .price-card .text-5xl { font-size: 2.5rem; }
    input, select, textarea {
        font-size: 16px;
        padding: 0.875rem;
    }
    .glass-card {
        padding: 1.5rem;
        margin: 1rem;
    }
    .mobile-menu .px-6 {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    #review-card {
        padding: 1.5rem;
        margin: 0 1rem;
    }
    #prev-review, #next-review {
        width: 2.5rem;
        height: 2.5rem;
        left: -0.5rem;
        right: -0.5rem;
    }
}

/* Extra Small Mobile (up to 375px) */
@media (max-width: 375px) {
    .container {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
    .hero-section h1 {
        font-size: 2rem !important;
    }
    .service-card, .price-card, .glass-card {
        padding: 1rem;
    }
    .glass-card {
        margin: 0.5rem;
    }
}

/* Very Small Mobile (up to 320px) */
@media (max-width: 320px) {
    .container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
    .hero-section h1 {
        font-size: 1.75rem !important;
        line-height: 1.2;
    }
    .mobile-bottom-nav-item {
        padding: 0.25rem;
        min-height: 50px;
    }
    .mobile-bottom-nav-item i {
        font-size: 1rem;
    }
    .mobile-bottom-nav-item span {
        font-size: 0.5rem;
    }

    /* Modern nav adjustments for very small screens */
    .mobile-nav-pill {
        min-width: 260px;
        padding: 6px;
        backdrop-filter: blur(30px) saturate(150%);
        -webkit-backdrop-filter: blur(30px) saturate(150%);
    }
    .mobile-nav-item {
        padding: 10px 10px;
        min-height: 48px;
    }
    .mobile-nav-item i {
        font-size: 1rem;
    }
    .mobile-nav-item span {
        font-size: 0.6rem;
    }
    .mobile-nav-item.active i,
    .mobile-nav-item:hover i,
    .mobile-nav-item.hover i {
        font-size: 1.1rem;
    }
    .dashboard-grid.four-col {
        grid-template-columns: 1fr;
    }
    .dashboard-icon-btn {
        min-width: 44px;
        min-height: 44px;
        padding: 0.75rem;
    }
    .dashboard-icon-btn.has-text {
        padding: 0.75rem 1rem;
        font-size: 0.8125rem;
    }
    .dashboard-btn-group {
        flex-direction: column;
        width: 100%;
    }
    .dashboard-btn-group .dashboard-icon-btn {
        width: 100%;
        justify-content: center;
    }
}

/* High DPI (Retina) Screens - Enhanced Glassmorphism */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .mobile-bottom-nav {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    .mobile-bottom-nav-modern {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    .mobile-nav-pill {
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.15),
            0 4px 16px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        backdrop-filter: blur(50px) saturate(200%);
        -webkit-backdrop-filter: blur(50px) saturate(200%);
    }
    .mobile-nav-item.active {
        box-shadow:
            0 8px 24px rgba(249, 115, 22, 0.5),
            0 4px 12px rgba(249, 115, 22, 0.4);
    }
}

/* Accessibility Preferences */
@media (prefers-contrast: high) {
    .dashboard-icon-btn {
        border-width: 2px;
    }
    .dashboard-icon-btn:focus {
        outline-width: 3px;
    }
}

@media (prefers-reduced-motion: reduce) {
    .dashboard-icon-btn, button, a {
        transition: none;
    }
    .dashboard-icon-btn:active, button:active, a:active {
        transform: none;
    }
    .dashboard-icon-btn.loading i, .spinner {
        animation: none;
    }
}

/* ===== 12. FINAL OVERRIDES & VIEWPORT CONSTRAINTS ===== */
@media (max-width: 768px) {
    /* Prevent any element from causing horizontal scroll */
    .dashboard-container,
    .dashboard-content,
    .container {
        max-width: 100vw;
        overflow-x: hidden;
    }
    /* Ensure all text content wraps properly */
    .dashboard-card, .dashboard-stat-card, .dashboard-table-container, .dashboard-filters {
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
    }
    /* Fix any potential overflow from flex/grid containers */
    .flex, .grid {
        min-width: 0;
    }
    .flex > *, .grid > * {
        min-width: 0;
        flex-shrink: 1;
    }
    /* Fix table, form, button, and media overflow */
    .dashboard-table-container, input, select, textarea, .dashboard-header-actions, img, video, canvas, svg {
        max-width: 100%;
    }
    /* Fix pre and code blocks */
    pre, code {
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    /* Ensure absolute positioned elements stay within viewport */
    .absolute {
        max-width: 100%;
    }
    /* Final catch-all for margin/padding overflow */
    .dashboard-content > * {
        max-width: 100%;
    }
}
