<?php $__env->startSection('title', 'Manage Bookings - Admin'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    
    <!-- Header -->
    <div class="bg-white shadow-sm" style="padding-top: 12vh;">
        <div class="container mx-auto px-4 sm:px-6 py-6 sm:py-8">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center">
                <div class="mb-4 lg:mb-0">
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Manage Bookings</h1>
                    <p class="text-gray-600 mt-1 text-sm sm:text-base">View and manage all delivery bookings</p>
                </div>
                <div class="dashboard-header-actions">
                    <a href="<?php echo e(route('admin.dashboard')); ?>"
                       class="dashboard-icon-btn secondary has-text">
                        <i class="fas fa-arrow-left"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 sm:px-6 py-6 sm:py-8 dashboard-content">
        <!-- Filters -->
        <div class="dashboard-filters">
            <form method="GET" action="<?php echo e(route('admin.bookings.index')); ?>" class="space-y-4">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select name="status" id="status" class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm">
                            <option value="">All Statuses</option>
                            <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                            <option value="confirmed" <?php echo e(request('status') == 'confirmed' ? 'selected' : ''); ?>>Confirmed</option>
                            <option value="in_progress" <?php echo e(request('status') == 'in_progress' ? 'selected' : ''); ?>>In Progress</option>
                            <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>Completed</option>
                            <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                        </select>
                    </div>
                        
                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                            <input type="date" name="date_from" id="date_from" value="<?php echo e(request('date_from')); ?>" 
                                   class="w-full border border-gray-300 rounded-lg px-3 py-2">
                        </div>
                        
                        <div>
                            <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                            <input type="date" name="date_to" id="date_to" value="<?php echo e(request('date_to')); ?>" 
                                   class="w-full border border-gray-300 rounded-lg px-3 py-2">
                        </div>
                        
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                            <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>" 
                                   placeholder="Booking ID or customer name"
                                   class="w-full border border-gray-300 rounded-lg px-3 py-2">
                        </div>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button type="submit" class="brand-orange text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                            <i class="fas fa-search mr-2"></i>Filter
                        </button>
                        <a href="<?php echo e(route('admin.bookings.index')); ?>" 
                           class="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                            Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Status Tabs -->
        <div class="bg-white rounded-xl shadow-sm mb-6">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6" aria-label="Tabs">
                    <a href="<?php echo e(route('admin.bookings.index')); ?>" 
                       class="py-4 px-1 border-b-2 font-medium text-sm <?php echo e(!request('status') ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                        All (<?php echo e($statusCounts['all']); ?>)
                    </a>
                    <a href="<?php echo e(route('admin.bookings.index', ['status' => 'pending'])); ?>" 
                       class="py-4 px-1 border-b-2 font-medium text-sm <?php echo e(request('status') == 'pending' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                        Pending (<?php echo e($statusCounts['pending']); ?>)
                    </a>
                    <a href="<?php echo e(route('admin.bookings.index', ['status' => 'confirmed'])); ?>" 
                       class="py-4 px-1 border-b-2 font-medium text-sm <?php echo e(request('status') == 'confirmed' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                        Confirmed (<?php echo e($statusCounts['confirmed']); ?>)
                    </a>

                    <a href="<?php echo e(route('admin.bookings.index', ['status' => 'completed'])); ?>" 
                       class="py-4 px-1 border-b-2 font-medium text-sm <?php echo e(request('status') == 'completed' ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                        Completed (<?php echo e($statusCounts['completed']); ?>)
                    </a>
                </nav>
            </div>
        </div>

        <!-- Bookings Table -->
        <div class="bg-white rounded-xl shadow-sm">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Booking</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>

                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Route</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php $__empty_1 = true; $__currentLoopData = $bookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900"><?php echo e($booking->booking_id); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo e($booking->package_type); ?></div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900"><?php echo e($booking->customer->name); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo e($booking->customer->email); ?></div>
                                    </div>
                                </td>

                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900">
                                        <div class="truncate max-w-xs">From: <?php echo e($booking->pickup_address); ?></div>
                                        <div class="truncate max-w-xs text-gray-500">To: <?php echo e($booking->delivery_address); ?></div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        <?php switch($booking->status):
                                            case ('pending'): ?>
                                                bg-yellow-100 text-yellow-800
                                                <?php break; ?>
                                            <?php case ('confirmed'): ?>
                                                bg-blue-100 text-blue-800
                                                <?php break; ?>

                                            <?php case ('in_progress'): ?>
                                                bg-purple-100 text-purple-800
                                                <?php break; ?>
                                            <?php case ('completed'): ?>
                                                bg-green-100 text-green-800
                                                <?php break; ?>
                                            <?php case ('cancelled'): ?>
                                                bg-red-100 text-red-800
                                                <?php break; ?>
                                            <?php default: ?>
                                                bg-gray-100 text-gray-800
                                        <?php endswitch; ?>
                                    ">
                                        <?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?>

                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo e(\App\Models\Setting::formatCurrency($booking->final_cost ?? $booking->estimated_cost)); ?>

                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo e($booking->created_at->format('M d, Y')); ?>

                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="<?php echo e(route('admin.bookings.show', $booking)); ?>" 
                                       class="text-orange-600 hover:text-orange-900">View</a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="8" class="px-6 py-12 text-center">
                                    <div class="text-gray-500">
                                        <i class="fas fa-inbox text-4xl mb-4"></i>
                                        <p>No bookings found</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <?php if($bookings->hasPages()): ?>
                <div class="px-6 py-4 border-t border-gray-200">
                    <?php echo e($bookings->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/admin/bookings/index.blade.php ENDPATH**/ ?>