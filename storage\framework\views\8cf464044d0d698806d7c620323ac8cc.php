
<nav class="mobile-bottom-nav-modern">
    <div class="mobile-nav-pill">

        
        <a href="<?php echo e(route('home')); ?>"
           class="mobile-nav-item <?php echo e(request()->routeIs('home') ? 'active' : ''); ?>">
            <i class="fas fa-house"></i>
            <span>Home</span>
        </a>

        
        <a href="<?php echo e(route('booking.create')); ?>"
           class="mobile-nav-item <?php echo e(request()->routeIs('booking.create') ? 'active' : ''); ?>">
            <i class="fas fa-calendar-plus"></i>
            <span>Book</span>
        </a>

        
        <a href="<?php echo e(route('tracking')); ?>"
           class="mobile-nav-item <?php echo e(request()->routeIs('tracking') ? 'active' : ''); ?>">
            <i class="fas fa-location-dot"></i>
            <span>Track</span>
        </a>

        
        <?php if(auth()->guard()->guest()): ?>
            <a href="<?php echo e(route('login')); ?>"
               class="mobile-nav-item <?php echo e(request()->routeIs('login') ? 'active' : ''); ?>">
                <i class="fas fa-user"></i>
                <span>Login</span>
            </a>
        <?php else: ?>
            <?php if(auth()->user()->isCustomer()): ?>
                <a href="<?php echo e(route('customer.dashboard')); ?>"
                   class="mobile-nav-item <?php echo e(request()->routeIs('customer.dashboard') ? 'active' : ''); ?>">
                    <i class="fas fa-circle-user"></i>
                    <span>Account</span>
                </a>
            <?php elseif(auth()->user()->isAdmin()): ?>
                <a href="<?php echo e(route('admin.dashboard')); ?>"
                   class="mobile-nav-item <?php echo e(request()->routeIs('admin.dashboard') ? 'active' : ''); ?>">
                    <i class="fas fa-gauge"></i>
                    <span>Admin</span>
                </a>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</nav>


<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const navItems = document.querySelectorAll('.mobile-nav-item');

    // Function to update active states
    function updateActiveStates() {
        const currentPath = window.location.pathname;
        const currentHash = window.location.hash;

        navItems.forEach(item => {
            const href = item.getAttribute('href');
            item.classList.remove('active');

            // Check for exact path match
            if (href === currentPath) {
                item.classList.add('active');
            }

            // Check for hash match
            if (href.includes('#') && href.endsWith(currentHash)) {
                item.classList.add('active');
            }
        });
    }

    // Enhanced hover and interaction effects
    navItems.forEach(item => {
        // Mouse enter - add hover effect
        item.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.classList.add('hover');
            }
        });

        // Mouse leave - remove hover effect
        item.addEventListener('mouseleave', function() {
            this.classList.remove('hover');
        });

        // Touch start - add pressed effect for mobile
        item.addEventListener('touchstart', function() {
            this.classList.add('pressed');
        });

        // Touch end - remove pressed effect
        item.addEventListener('touchend', function() {
            setTimeout(() => {
                this.classList.remove('pressed');
            }, 150);
        });

        // Click handling with visual feedback
        item.addEventListener('click', function(e) {
            const href = this.getAttribute('href');

            // Add click animation
            this.classList.add('clicked');
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 200);

            // Handle hash links with smooth scroll
            if (href.includes('#')) {
                const targetId = href.split('#')[1];
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    e.preventDefault();
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });

                    // Update URL hash
                    history.pushState(null, null, href);
                    updateActiveStates();
                }
            }
        });
    });

    // Update on page load
    updateActiveStates();

    // Update on hash change
    window.addEventListener('hashchange', updateActiveStates);

    // Update on popstate (browser back/forward)
    window.addEventListener('popstate', updateActiveStates);
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/components/mobile-bottom-nav.blade.php ENDPATH**/ ?>