<?php $__env->startSection('title', 'Admin Dashboard - TTAJet'); ?>

<?php $__env->startPush('head-scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>

<style>
    main {
        margin-top: 0px !important;
    }
    </style>

<div class="min-h-screen bg-gray-50">

    <!-- Header -->
    <div class="bg-white shadow-sm" style="padding-top: 12vh;">
        <div class="container mx-auto px-4 sm:px-6 py-6 sm:py-8">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center">
                <div class="mb-4 lg:mb-0">
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Admin Dashboard</h1>
                    <p class="text-gray-600 mt-1 text-sm sm:text-base">Monitor and manage your courier service operations</p>
                </div>
                <div class="dashboard-header-actions">
                    <a href="<?php echo e(route('live-map.index')); ?>"
                       class="dashboard-icon-btn primary has-text">
                        <i class="fas fa-map"></i>Live Map
                    </a>
                    <a href="<?php echo e(route('admin.bookings.index')); ?>"
                       class="dashboard-icon-btn secondary has-text">
                        <i class="fas fa-list"></i>Manage Bookings
                    </a>
                    <a href="<?php echo e(route('admin.users.index')); ?>"
                       class="dashboard-icon-btn secondary has-text">
                        <i class="fas fa-users"></i>Manage Users
                    </a>
                    <a href="<?php echo e(route('admin.settings')); ?>"
                       class="dashboard-icon-btn secondary has-text">
                        <i class="fas fa-cogs"></i>Settings
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 sm:px-6 py-6 sm:py-8 dashboard-content">

        <!-- Key Statistics -->
        <div class="dashboard-grid four-col mb-6 sm:mb-8">
            <div class="dashboard-stat-card">
                <div class="flex items-center">
                    <div class="p-2 sm:p-3 bg-blue-100 rounded-full flex-shrink-0">
                        <i class="fas fa-box text-blue-600 text-lg sm:text-xl"></i>
                    </div>
                    <div class="ml-3 sm:ml-4 min-w-0">
                        <p class="text-gray-500 text-xs sm:text-sm">Total Bookings</p>
                        <p class="text-xl sm:text-2xl font-bold text-gray-900"><?php echo e($stats['total_bookings']); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-xl shadow-sm">
                <div class="flex items-center">
                    <div class="p-3 bg-yellow-100 rounded-full">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-500 text-sm">Pending</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['pending_bookings']); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-xl shadow-sm">
                <div class="flex items-center">
                    <div class="p-3 bg-purple-100 rounded-full">
                        <i class="fas fa-truck text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-gray-500 text-sm">Active</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['active_bookings']); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-xl shadow-sm brand-orange text-white">
                <div class="flex items-center">
                    <div class="p-3 bg-white bg-opacity-20 rounded-full">
                        <i class="fas fa-motorcycle text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Revenue Cards -->
        <div class="grid sm:grid-cols-3 gap-6 mb-8">
            <div class="bg-white p-6 rounded-xl shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">Today's Revenue</p>
                        <p class="text-2xl font-bold text-green-600"><?php echo e(\App\Models\Setting::formatCurrency($stats['revenue_today'])); ?></p>
                    </div>
                    <i class="fas fa-dollar-sign text-green-500 text-2xl"></i>
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-xl shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">This Week</p>
                        <p class="text-2xl font-bold text-green-600"><?php echo e(\App\Models\Setting::formatCurrency($stats['revenue_week'])); ?></p>
                    </div>
                    <i class="fas fa-chart-line text-green-500 text-2xl"></i>
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-xl shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">This Month</p>
                        <p class="text-2xl font-bold text-green-600"><?php echo e(\App\Models\Setting::formatCurrency($stats['revenue_month'])); ?></p>
                    </div>
                    <i class="fas fa-calendar text-green-500 text-2xl"></i>
                </div>
            </div>
        </div>
        
        <div class="grid lg:grid-cols-3 gap-8">
            
            <!-- Charts Section -->
            <div class="lg:col-span-2 space-y-8">
                
                <!-- Booking Trends Chart -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Booking Trends (Last 7 Days)</h3>
                    </div>
                    <div class="p-6">
                        <canvas id="bookingTrendsChart" height="100"></canvas>
                    </div>
                </div>
                
                <!-- Recent Bookings -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-bold text-gray-900">Recent Bookings</h3>
                            <a href="<?php echo e(route('admin.bookings.index')); ?>" class="text-orange-500 hover:text-orange-600 text-sm font-semibold">
                                View All
                            </a>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Booking ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php $__currentLoopData = $recentBookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="font-medium text-gray-900"><?php echo e($booking->booking_id); ?></span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="text-gray-900"><?php echo e($booking->customer->name); ?></span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                <?php if($booking->status === 'pending'): ?> bg-yellow-100 text-yellow-800
                                                <?php elseif($booking->status === 'assigned'): ?> bg-blue-100 text-blue-800
                                                <?php elseif(in_array($booking->status, ['picked_up', 'in_transit'])): ?> bg-purple-100 text-purple-800
                                                <?php elseif($booking->status === 'delivered'): ?> bg-green-100 text-green-800
                                                <?php else: ?> bg-gray-100 text-gray-800
                                                <?php endif; ?>">
                                                <?php echo e($booking->formatted_status); ?>

                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="text-gray-900"><?php echo e(\App\Models\Setting::formatCurrency($booking->estimated_cost)); ?></span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <a href="<?php echo e(route('admin.bookings.show', $booking)); ?>" 
                                               class="text-orange-600 hover:text-orange-900">View</a>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="space-y-6">
                
                <!-- Pending Assignments -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Pending Assignments</h3>
                    </div>
                    <div class="p-6">
                        <?php if($pendingBookings->count() > 0): ?>
                            <div class="space-y-3">
                                <?php $__currentLoopData = $pendingBookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <p class="font-semibold text-sm"><?php echo e($booking->booking_id); ?></p>
                                                <p class="text-xs text-gray-600"><?php echo e($booking->customer->name); ?></p>
                                                <p class="text-xs text-gray-500 mt-1"><?php echo e($booking->created_at->diffForHumans()); ?></p>
                                            </div>
                                            <a href="<?php echo e(route('admin.bookings.show', $booking)); ?>" 
                                               class="text-orange-600 hover:text-orange-800 text-xs">
                                                Assign
                                            </a>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <p class="text-gray-500 text-sm text-center py-4">No pending assignments</p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- System Status -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">System Status</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-semibold text-sm">Delivery Service</p>
                                    <p class="text-xs text-gray-500">All systems operational</p>
                                </div>
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-semibold text-sm">Booking System</p>
                                    <p class="text-xs text-gray-500">Online and accepting orders</p>
                                </div>
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Stats -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Quick Stats</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Total Customers</span>
                            <span class="font-semibold"><?php echo e($stats['total_customers']); ?></span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">Delivered Today</span>
                            <span class="font-semibold"><?php echo e($stats['delivered_today']); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Booking Trends Chart
    const bookingCtx = document.getElementById('bookingTrendsChart').getContext('2d');
    const bookingTrends = <?php echo json_encode($bookingTrends, 15, 512) ?>;
    
    new Chart(bookingCtx, {
        type: 'line',
        data: {
            labels: bookingTrends.map(item => item.date),
            datasets: [{
                label: 'Bookings',
                data: bookingTrends.map(item => item.count),
                borderColor: '#F97316',
                backgroundColor: 'rgba(249, 115, 22, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
    
    // Animate cards
    gsap.from(".bg-white", {
        duration: 0.6,
        y: 30,
        opacity: 0,
        stagger: 0.1,
        ease: 'power2.out'
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>