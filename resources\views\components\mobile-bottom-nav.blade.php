{{-- Mobile Bottom Navigation Component --}}
<nav class="mobile-bottom-nav-modern">
    <div class="mobile-nav-pill">

        {{-- Home --}}
        <a href="{{ route('home') }}"
           class="mobile-nav-item {{ request()->routeIs('home') || request()->is('/') ? 'active' : '' }}">
            <i class="fas fa-house"></i>
        </a>

        {{-- Book Delivery --}}
        <a href="{{ route('booking.create') }}"
           class="mobile-nav-item {{ request()->routeIs('booking.*') || request()->is('booking*') ? 'active' : '' }}">
            <i class="fa-solid fa-calendar-plus"></i>
        </a>

        {{-- Track --}}
        <a href="{{ route('tracking') }}"
           class="mobile-nav-item {{ request()->routeIs('tracking*') || request()->is('tracking*') || request()->is('track*') ? 'active' : '' }}">
            <i class="fa-solid fa-location-pin"></i>
        </a>

        {{-- Profile/Account --}}
        @guest
            <a href="{{ route('login') }}"
               class="mobile-nav-item {{ request()->routeIs('login') || request()->routeIs('register') || request()->is('login*') || request()->is('register*') ? 'active' : '' }}">
                <i class="fa-solid fa-right-to-bracket"></i>
            </a>
        @else
            @if(auth()->user()->isCustomer())
                <a href="{{ route('customer.dashboard') }}"
                   class="mobile-nav-item {{ request()->routeIs('customer.*') || request()->is('customer*') || request()->is('dashboard*') ? 'active' : '' }}">
                    <i class="fa-solid fa-circle-user"></i>
                </a>
            @elseif(auth()->user()->isAdmin())
                <a href="{{ route('admin.dashboard') }}"
                   class="mobile-nav-item {{ request()->routeIs('admin.*') || request()->is('admin*') ? 'active' : '' }}">
                    <i class="fa-solid fa-bars-progress"></i>
                </a>
            @endif
        @endguest
    </div>
</nav>

{{-- Enhanced JavaScript for active and hover state management --}}
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const navItems = document.querySelectorAll('.mobile-nav-item');

    // Helper function to normalize URLs for comparison
    function normalizeUrl(url) {
        if (!url) return '';
        // Remove trailing slashes and convert to lowercase
        return url.replace(/\/$/, '').toLowerCase();
    }

    // Helper function to extract path without hash
    function getPathFromUrl(url) {
        if (!url) return '';
        return url.split('#')[0].split('?')[0];
    }

    // Function to check if a nav item should be active
    function isNavItemActive(navHref, currentPath, currentHash) {
        if (!navHref) return false;

        const normalizedNavHref = normalizeUrl(navHref);
        const normalizedCurrentPath = normalizeUrl(currentPath);

        // Handle hash-based navigation (like #services)
        if (navHref.includes('#')) {
            const navPath = normalizeUrl(getPathFromUrl(navHref));
            const navHash = navHref.split('#')[1];

            // Check if we're on the right page and hash
            if (navPath === normalizedCurrentPath && currentHash === '#' + navHash) {
                return true;
            }
            // Also check if we're on the base page without hash
            if (navPath === normalizedCurrentPath && !currentHash) {
                return false; // Don't activate hash links when no hash is present
            }
        }

        // Handle exact path matches
        if (normalizedNavHref === normalizedCurrentPath) {
            return true;
        }

        // Handle nested route patterns (like dashboard routes)
        const navPath = getPathFromUrl(normalizedNavHref);

        // Special cases for common route patterns
        if (navPath === '/home' || navPath === '' || navPath === '/') {
            return normalizedCurrentPath === '/home' || normalizedCurrentPath === '' || normalizedCurrentPath === '/';
        }

        if (navPath === '/login') {
            return normalizedCurrentPath === '/login' || normalizedCurrentPath === '/register';
        }

        if (navPath.includes('/dashboard')) {
            return normalizedCurrentPath.includes('/dashboard');
        }

        if (navPath.includes('/booking')) {
            return normalizedCurrentPath.includes('/booking');
        }

        if (navPath.includes('/tracking')) {
            return normalizedCurrentPath.includes('/tracking') || normalizedCurrentPath.includes('/track');
        }

        return false;
    }

    // Function to update active states
    function updateActiveStates() {
        const currentPath = window.location.pathname;
        const currentHash = window.location.hash;

        // Debug logging (remove in production)
        console.log('Updating active states:', { currentPath, currentHash });

        navItems.forEach(item => {
            const href = item.getAttribute('href');
            const wasActive = item.classList.contains('active');

            item.classList.remove('active');

            if (isNavItemActive(href, currentPath, currentHash)) {
                item.classList.add('active');
                if (!wasActive) {
                    console.log('Activated nav item:', href);
                }
            }
        });
    }

    // Enhanced hover and interaction effects
    navItems.forEach(item => {
        // Mouse enter - add hover effect
        item.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.classList.add('hover');
            }
        });

        // Mouse leave - remove hover effect
        item.addEventListener('mouseleave', function() {
            this.classList.remove('hover');
        });

        // Touch start - add pressed effect for mobile
        item.addEventListener('touchstart', function() {
            this.classList.add('pressed');
        }, { passive: true });

        // Touch end - remove pressed effect
        item.addEventListener('touchend', function() {
            setTimeout(() => {
                this.classList.remove('pressed');
            }, 150);
        }, { passive: true });

        // Click handling with visual feedback
        item.addEventListener('click', function(e) {
            const href = this.getAttribute('href');

            // Add click animation
            this.classList.add('clicked');
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 200);

            // Handle hash links with smooth scroll
            if (href && href.includes('#') && !href.startsWith('http')) {
                const hashPart = href.split('#')[1];
                const targetElement = document.getElementById(hashPart);

                if (targetElement) {
                    e.preventDefault();
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });

                    // Update URL hash
                    const newUrl = href;
                    history.pushState(null, null, newUrl);

                    // Update active states after a short delay to ensure URL is updated
                    setTimeout(updateActiveStates, 100);
                }
            } else {
                // For regular navigation, update active states after a short delay
                setTimeout(updateActiveStates, 100);
            }
        });
    });

    // Initial update on page load
    updateActiveStates();

    // Update on hash change
    window.addEventListener('hashchange', function() {
        setTimeout(updateActiveStates, 50);
    });

    // Update on popstate (browser back/forward)
    window.addEventListener('popstate', function() {
        setTimeout(updateActiveStates, 50);
    });

    // Update on page visibility change (when returning to tab)
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            setTimeout(updateActiveStates, 100);
        }
    });

    // Fallback: periodically check for active state (useful for SPA navigation)
    let lastPath = window.location.pathname;
    let lastHash = window.location.hash;

    setInterval(function() {
        const currentPath = window.location.pathname;
        const currentHash = window.location.hash;

        if (currentPath !== lastPath || currentHash !== lastHash) {
            lastPath = currentPath;
            lastHash = currentHash;
            updateActiveStates();
        }
    }, 1000);
});
</script>
@endpush
