{{-- Mobile Bottom Navigation Component --}}
<nav class="mobile-bottom-nav-modern">
    <div class="mobile-nav-pill">

        {{-- Home --}}
        <a href="{{ route('home') }}"
           class="mobile-nav-item {{ request()->routeIs('home') ? 'active' : '' }}">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>

        {{-- Services --}}
        <a href="{{ route('home') }}#services"
           class="mobile-nav-item">
            <i class="fas fa-shipping-fast"></i>
            <span>Services</span>
        </a>

        {{-- Book Delivery (Center - Primary Action) --}}
        <a href="{{ route('booking.create') }}"
           class="mobile-nav-item {{ request()->routeIs('booking.create') ? 'active' : '' }}">
            <i class="fas fa-plus"></i>
            <span>Book</span>
        </a>

        {{-- Track --}}
        <a href="{{ route('tracking') }}"
           class="mobile-nav-item {{ request()->routeIs('tracking') ? 'active' : '' }}">
            <i class="fas fa-search-location"></i>
            <span>Track</span>
        </a>

        {{-- Profile/Account --}}
        @guest
            <a href="{{ route('login') }}"
               class="mobile-nav-item {{ request()->routeIs('login') ? 'active' : '' }}">
                <i class="fas fa-user"></i>
                <span>Login</span>
            </a>
        @else
            @if(auth()->user()->isCustomer())
                <a href="{{ route('customer.dashboard') }}"
                   class="mobile-nav-item {{ request()->routeIs('customer.dashboard') ? 'active' : '' }}">
                    <i class="fas fa-user-circle"></i>
                    <span>Account</span>
                </a>
            @elseif(auth()->user()->isAdmin())
                <a href="{{ route('admin.dashboard') }}"
                   class="mobile-nav-item {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Admin</span>
                </a>
            @endif
        @endguest
    </div>
</nav>

{{-- JavaScript for active state management --}}
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle active states for anchor links (like #services)
    const navItems = document.querySelectorAll('.mobile-nav-item');

    // Function to update active states
    function updateActiveStates() {
        const currentPath = window.location.pathname;
        const currentHash = window.location.hash;

        navItems.forEach(item => {
            const href = item.getAttribute('href');
            item.classList.remove('active');

            // Check for exact path match
            if (href === currentPath) {
                item.classList.add('active');
            }

            // Check for hash match (like #services)
            if (href.includes('#') && href.endsWith(currentHash)) {
                item.classList.add('active');
            }
        });
    }

    // Update on page load
    updateActiveStates();

    // Update on hash change
    window.addEventListener('hashchange', updateActiveStates);

    // Add smooth scroll behavior for anchor links
    navItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href.includes('#')) {
            item.addEventListener('click', function(e) {
                const targetId = href.split('#')[1];
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    e.preventDefault();
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });

                    // Update URL hash
                    history.pushState(null, null, href);
                    updateActiveStates();
                }
            });
        }
    });
});
</script>
@endpush
