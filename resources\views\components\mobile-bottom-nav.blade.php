{{-- Mobile Bottom Navigation Component --}}
<nav class="mobile-bottom-nav-modern">
    <div class="mobile-nav-pill">

        {{-- Home --}}
        <a href="{{ route('home') }}"
           class="mobile-nav-item {{ request()->routeIs('home') || request()->is('/') || request()->is('home') || request()->is('home/*') || (!request()->is('booking*') && !request()->is('tracking*') && !request()->is('login*') && !request()->is('register*') && !request()->is('customer*') && !request()->is('admin*') && request()->path() === '/') ? 'active' : '' }}">
            <i class="fas fa-house"></i>
        </a>

        {{-- Book Delivery --}}
        <a href="{{ route('booking.create') }}"
           class="mobile-nav-item {{ request()->routeIs('booking.*') || request()->is('booking*') ? 'active' : '' }}">
            <i class="fa-solid fa-calendar-plus"></i>
        </a>

        {{-- Track --}}
        <a href="{{ route('tracking') }}"
           class="mobile-nav-item {{ request()->routeIs('tracking*') || request()->is('tracking*') || request()->is('track*') ? 'active' : '' }}">
            <i class="fa-solid fa-location-pin"></i>
        </a>

        {{-- Profile/Account --}}
        @guest
            <a href="{{ route('login') }}"
               class="mobile-nav-item {{ request()->routeIs('login') || request()->routeIs('register') || request()->is('login*') || request()->is('register*') ? 'active' : '' }}">
                <i class="fa-solid fa-right-to-bracket"></i>
            </a>
        @else
            @if(auth()->user()->isCustomer())
                <a href="{{ route('customer.dashboard') }}"
                   class="mobile-nav-item {{ request()->routeIs('customer.*') || request()->is('customer*') || request()->is('dashboard*') ? 'active' : '' }}">
                    <i class="fa-solid fa-circle-user"></i>
                </a>
            @elseif(auth()->user()->isAdmin())
                <a href="{{ route('admin.dashboard') }}"
                   class="mobile-nav-item {{ request()->routeIs('admin.*') || request()->is('admin*') ? 'active' : '' }}">
                    <i class="fa-solid fa-bars-progress"></i>
                </a>
            @endif
        @endguest
    </div>
</nav>

{{-- Enhanced JavaScript for active and hover state management --}}
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const navItems = document.querySelectorAll('.mobile-nav-item');

    // Define route patterns for each navigation item
    const routePatterns = {
        home: {
            paths: ['/', '/home'],
            routes: ['home'],
            contains: []
        },
        booking: {
            paths: ['/booking/create'],
            routes: ['booking.create', 'booking.edit', 'booking.show'],
            contains: ['/booking']
        },
        tracking: {
            paths: ['/tracking', '/track'],
            routes: ['tracking', 'tracking.show'],
            contains: ['/tracking', '/track']
        },
        login: {
            paths: ['/login', '/register'],
            routes: ['login', 'register'],
            contains: []
        },
        customer: {
            paths: ['/customer/dashboard'],
            routes: ['customer.dashboard', 'customer.profile', 'customer.orders'],
            contains: ['/customer', '/dashboard']
        },
        admin: {
            paths: ['/admin/dashboard'],
            routes: ['admin.dashboard', 'admin.users', 'admin.orders'],
            contains: ['/admin']
        }
    };

    // Function to determine which nav item should be active
    function getActiveNavType(currentPath, currentUrl) {
        const path = currentPath.toLowerCase();
        const fullUrl = currentUrl.toLowerCase();

        console.log('🏠 Checking path for active type:', { path, fullUrl });

        // Special handling for home page - check this first
        if (path === '' || path === '/' || path === '/home' ||
            path.endsWith('/') && path.slice(0, -1) === '' ||
            path === '/ttajetcom' || path.endsWith('/ttajetcom') ||
            fullUrl.includes('/home') && !fullUrl.includes('/booking') && !fullUrl.includes('/tracking') && !fullUrl.includes('/admin') && !fullUrl.includes('/customer')) {
            console.log('🏠 Detected HOME page');
            return 'home';
        }

        // Check contains patterns first (more specific)
        for (const [type, config] of Object.entries(routePatterns)) {
            if (config.contains.length > 0 && config.contains.some(pattern => path.includes(pattern))) {
                console.log(`📍 Detected ${type.toUpperCase()} via contains pattern`);
                return type;
            }
        }

        // Check exact path matches
        for (const [type, config] of Object.entries(routePatterns)) {
            if (config.paths.some(p => path === p || path === p + '/')) {
                console.log(`📍 Detected ${type.toUpperCase()} via exact path`);
                return type;
            }
        }

        console.log('❓ No specific type detected, defaulting to home');
        return 'home'; // Default to home if nothing else matches
    }

    // Function to update active states
    function updateActiveStates() {
        const currentPath = window.location.pathname;
        const currentUrl = window.location.href;

        console.log('🔍 Checking active states for:', currentPath);

        // Clear all active states first
        navItems.forEach(item => {
            item.classList.remove('active');
        });

        // Determine which type should be active
        const activeType = getActiveNavType(currentPath, currentUrl);
        console.log('📍 Active type determined:', activeType);

        if (!activeType) {
            console.log('❌ No active type found');
            return;
        }

        // Find and activate the corresponding nav item
        navItems.forEach(item => {
            const href = item.getAttribute('href');
            if (!href) return;

            let shouldActivate = false;
            const hrefLower = href.toLowerCase();

            // Check based on href patterns with improved home detection
            if (activeType === 'home') {
                // More flexible home detection
                if (hrefLower.includes('/home') || hrefLower === '/' ||
                    hrefLower.endsWith('/') && hrefLower.length <= 2 ||
                    hrefLower.includes('route(\'home\')') ||
                    item.querySelector('i.fa-house')) { // Also check by icon
                    shouldActivate = true;
                    console.log('🏠 Home nav item found:', href);
                }
            } else if (activeType === 'booking' && hrefLower.includes('/booking')) {
                shouldActivate = true;
            } else if (activeType === 'tracking' && hrefLower.includes('/tracking')) {
                shouldActivate = true;
            } else if (activeType === 'login' && hrefLower.includes('/login')) {
                shouldActivate = true;
            } else if (activeType === 'customer' && (hrefLower.includes('/customer') || hrefLower.includes('/dashboard'))) {
                shouldActivate = true;
            } else if (activeType === 'admin' && hrefLower.includes('/admin')) {
                shouldActivate = true;
            }

            if (shouldActivate) {
                item.classList.add('active');
                console.log('✅ Activated nav item:', href, 'for type:', activeType);
            }
        });
    }

    // Enhanced hover and interaction effects
    navItems.forEach(item => {
        // Mouse enter - add hover effect
        item.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.classList.add('hover');
            }
        });

        // Mouse leave - remove hover effect
        item.addEventListener('mouseleave', function() {
            this.classList.remove('hover');
        });

        // Touch start - add pressed effect for mobile
        item.addEventListener('touchstart', function() {
            this.classList.add('pressed');
        }, { passive: true });

        // Touch end - remove pressed effect
        item.addEventListener('touchend', function() {
            setTimeout(() => {
                this.classList.remove('pressed');
            }, 150);
        }, { passive: true });

        // Click handling with visual feedback
        item.addEventListener('click', function(e) {
            const href = this.getAttribute('href');

            // Add click animation
            this.classList.add('clicked');
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 200);

            // Update active states after navigation
            setTimeout(updateActiveStates, 200);
        });
    });

    // Force initial update with multiple attempts
    setTimeout(updateActiveStates, 100);
    setTimeout(updateActiveStates, 500);
    setTimeout(updateActiveStates, 1000);

    // Update on various events
    window.addEventListener('load', updateActiveStates);
    window.addEventListener('hashchange', updateActiveStates);
    window.addEventListener('popstate', updateActiveStates);
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            updateActiveStates();
        }
    });

    // Continuous monitoring for SPA-style navigation
    let lastPath = window.location.pathname;
    setInterval(function() {
        const currentPath = window.location.pathname;
        if (currentPath !== lastPath) {
            lastPath = currentPath;
            console.log('🔄 Path changed to:', currentPath);
            updateActiveStates();
        }
    }, 500);

    // Debug functions
    window.updateMobileNavActiveStates = updateActiveStates;
    window.debugMobileNav = function() {
        console.log('🔍 DEBUG INFO:');
        console.log('Current pathname:', window.location.pathname);
        console.log('Current href:', window.location.href);
        console.log('Nav items found:', navItems.length);

        navItems.forEach((item, index) => {
            const href = item.getAttribute('href');
            const hasActive = item.classList.contains('active');
            const icon = item.querySelector('i') ? item.querySelector('i').className : 'no icon';
            console.log(`Nav ${index + 1}: href="${href}", active=${hasActive}, icon="${icon}"`);
        });

        const activeType = getActiveNavType(window.location.pathname, window.location.href);
        console.log('Detected active type:', activeType);

        updateActiveStates();
    };

    console.log('🚀 Mobile nav JavaScript loaded. Current path:', window.location.pathname);
    console.log('💡 Use window.debugMobileNav() to troubleshoot active states');
});
</script>
@endpush
