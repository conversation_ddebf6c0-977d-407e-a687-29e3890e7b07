{{-- Mobile Bottom Navigation Component --}}
<nav class="mobile-bottom-nav-modern">
    <div class="mobile-nav-pill">

        {{-- Home --}}
        <a href="{{ route('home') }}"
           class="mobile-nav-item {{ request()->routeIs('home') || request()->is('/') ? 'active' : '' }}">
            <i class="fas fa-house"></i>
        </a>

        {{-- Book Delivery --}}
        <a href="{{ route('booking.create') }}"
           class="mobile-nav-item {{ request()->routeIs('booking.*') || request()->is('booking*') ? 'active' : '' }}">
            <i class="fa-solid fa-calendar-plus"></i>
        </a>

        {{-- Track --}}
        <a href="{{ route('tracking') }}"
           class="mobile-nav-item {{ request()->routeIs('tracking*') || request()->is('tracking*') || request()->is('track*') ? 'active' : '' }}">
            <i class="fa-solid fa-location-pin"></i>
        </a>

        {{-- Profile/Account --}}
        @guest
            <a href="{{ route('login') }}"
               class="mobile-nav-item {{ request()->routeIs('login') || request()->routeIs('register') || request()->is('login*') || request()->is('register*') ? 'active' : '' }}">
                <i class="fa-solid fa-right-to-bracket"></i>
            </a>
        @else
            @if(auth()->user()->isCustomer())
                <a href="{{ route('customer.dashboard') }}"
                   class="mobile-nav-item {{ request()->routeIs('customer.*') || request()->is('customer*') || request()->is('dashboard*') ? 'active' : '' }}">
                    <i class="fa-solid fa-circle-user"></i>
                </a>
            @elseif(auth()->user()->isAdmin())
                <a href="{{ route('admin.dashboard') }}"
                   class="mobile-nav-item {{ request()->routeIs('admin.*') || request()->is('admin*') ? 'active' : '' }}">
                    <i class="fa-solid fa-bars-progress"></i>
                </a>
            @endif
        @endguest
    </div>
</nav>

{{-- Enhanced JavaScript for active and hover state management --}}
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const navItems = document.querySelectorAll('.mobile-nav-item');

    // Define route patterns for each navigation item
    const routePatterns = {
        home: {
            paths: ['/', '/home'],
            routes: ['home'],
            contains: []
        },
        booking: {
            paths: ['/booking/create'],
            routes: ['booking.create', 'booking.edit', 'booking.show'],
            contains: ['/booking']
        },
        tracking: {
            paths: ['/tracking', '/track'],
            routes: ['tracking', 'tracking.show'],
            contains: ['/tracking', '/track']
        },
        login: {
            paths: ['/login', '/register'],
            routes: ['login', 'register'],
            contains: []
        },
        customer: {
            paths: ['/customer/dashboard'],
            routes: ['customer.dashboard', 'customer.profile', 'customer.orders'],
            contains: ['/customer', '/dashboard']
        },
        admin: {
            paths: ['/admin/dashboard'],
            routes: ['admin.dashboard', 'admin.users', 'admin.orders'],
            contains: ['/admin']
        }
    };

    // Function to determine which nav item should be active
    function getActiveNavType(currentPath, currentUrl) {
        const path = currentPath.toLowerCase();
        const fullUrl = currentUrl.toLowerCase();

        // Check exact path matches first
        for (const [type, config] of Object.entries(routePatterns)) {
            if (config.paths.some(p => path === p || path === p + '/')) {
                return type;
            }
        }

        // Check contains patterns
        for (const [type, config] of Object.entries(routePatterns)) {
            if (config.contains.some(pattern => path.includes(pattern))) {
                return type;
            }
        }

        // Default fallback - if we're on root or home-like paths
        if (path === '' || path === '/' || path === '/home') {
            return 'home';
        }

        return null;
    }

    // Function to update active states
    function updateActiveStates() {
        const currentPath = window.location.pathname;
        const currentUrl = window.location.href;

        console.log('🔍 Checking active states for:', currentPath);

        // Clear all active states first
        navItems.forEach(item => {
            item.classList.remove('active');
        });

        // Determine which type should be active
        const activeType = getActiveNavType(currentPath, currentUrl);
        console.log('📍 Active type determined:', activeType);

        if (!activeType) {
            console.log('❌ No active type found');
            return;
        }

        // Find and activate the corresponding nav item
        navItems.forEach(item => {
            const href = item.getAttribute('href');
            if (!href) return;

            let shouldActivate = false;

            // Check based on href patterns
            if (activeType === 'home' && (href.includes('/home') || href === '/')) {
                shouldActivate = true;
            } else if (activeType === 'booking' && href.includes('/booking')) {
                shouldActivate = true;
            } else if (activeType === 'tracking' && href.includes('/tracking')) {
                shouldActivate = true;
            } else if (activeType === 'login' && href.includes('/login')) {
                shouldActivate = true;
            } else if (activeType === 'customer' && href.includes('/customer')) {
                shouldActivate = true;
            } else if (activeType === 'admin' && href.includes('/admin')) {
                shouldActivate = true;
            }

            if (shouldActivate) {
                item.classList.add('active');
                console.log('✅ Activated nav item:', href);
            }
        });
    }

    // Enhanced hover and interaction effects
    navItems.forEach(item => {
        // Mouse enter - add hover effect
        item.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.classList.add('hover');
            }
        });

        // Mouse leave - remove hover effect
        item.addEventListener('mouseleave', function() {
            this.classList.remove('hover');
        });

        // Touch start - add pressed effect for mobile
        item.addEventListener('touchstart', function() {
            this.classList.add('pressed');
        }, { passive: true });

        // Touch end - remove pressed effect
        item.addEventListener('touchend', function() {
            setTimeout(() => {
                this.classList.remove('pressed');
            }, 150);
        }, { passive: true });

        // Click handling with visual feedback
        item.addEventListener('click', function(e) {
            const href = this.getAttribute('href');

            // Add click animation
            this.classList.add('clicked');
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 200);

            // Update active states after navigation
            setTimeout(updateActiveStates, 200);
        });
    });

    // Force initial update with multiple attempts
    setTimeout(updateActiveStates, 100);
    setTimeout(updateActiveStates, 500);
    setTimeout(updateActiveStates, 1000);

    // Update on various events
    window.addEventListener('load', updateActiveStates);
    window.addEventListener('hashchange', updateActiveStates);
    window.addEventListener('popstate', updateActiveStates);
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            updateActiveStates();
        }
    });

    // Continuous monitoring for SPA-style navigation
    let lastPath = window.location.pathname;
    setInterval(function() {
        const currentPath = window.location.pathname;
        if (currentPath !== lastPath) {
            lastPath = currentPath;
            console.log('🔄 Path changed to:', currentPath);
            updateActiveStates();
        }
    }, 500);

    // Manual trigger function for debugging
    window.updateMobileNavActiveStates = updateActiveStates;
    console.log('🚀 Mobile nav JavaScript loaded. Current path:', window.location.pathname);
});
</script>
@endpush
